package io.despicable.chromeos.presentation.viewmodel

import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import io.despicable.chromeos.domain.model.*
import io.despicable.chromeos.domain.usecase.GetWeatherDataUseCase
import io.despicable.chromeos.domain.usecase.UpdateWidgetConfigUseCase
import io.despicable.chromeos.util.SampleWeatherData
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch

/**
 * ViewModel for widget configuration screen with real-time preview
 */
class WidgetConfigViewModel(
    private val widgetId: Int,
    private val getWeatherDataUseCase: GetWeatherDataUseCase,
    private val updateWidgetConfigUseCase: UpdateWidgetConfigUseCase
) : ViewModel() {

    companion object {
        private const val TAG = "WidgetConfigViewModel"
    }

    // UI State
    private val _uiState = MutableStateFlow(WidgetConfigUiState())
    val uiState: StateFlow<WidgetConfigUiState> = _uiState.asStateFlow()

    // Available cities
    private val _availableCities = MutableStateFlow<List<String>>(emptyList())
    val availableCities: StateFlow<List<String>> = _availableCities.asStateFlow()

    // Current weather data for preview
    private val _previewWeatherData = MutableStateFlow<WeatherData?>(null)
    val previewWeatherData: StateFlow<WeatherData?> = _previewWeatherData.asStateFlow()

    init {
        loadInitialData()
        observeCitySelection()
    }

    private fun loadInitialData() {
        viewModelScope.launch {
            try {
                // Load available cities
                getWeatherDataUseCase.refreshWeatherData()

                // Set available cities immediately
                val cities = SampleWeatherData.getAvailableCities()
                _availableCities.value = cities

                // Load existing widget configuration or create default
                val existingConfig = updateWidgetConfigUseCase.getWidgetConfig(widgetId)

                val defaultCity = cities.firstOrNull() ?: "New York"

                val newState = _uiState.value.copy(
                    selectedCity = existingConfig?.cityName ?: defaultCity,
                    selectedIconSize = existingConfig?.iconSize ?: IconSize.MEDIUM,
                    selectedFontSize = existingConfig?.fontSize ?: FontSize.MEDIUM,
                    selectedBackgroundColor = existingConfig?.backgroundColor ?: BackgroundColor.SYSTEM,
                    transparency = existingConfig?.transparency ?: 0.9f,
                    showHumidity = existingConfig?.showHumidity ?: true,
                    showWindSpeed = existingConfig?.showWindSpeed ?: true,
                    isLoading = false
                )
                _uiState.value = newState

                // Load initial weather data
                loadWeatherDataForCity(_uiState.value.selectedCity)
            } catch (e: Exception) {
                Log.e(TAG, "Error loading initial data", e)
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = e.message
                )
            }
        }
    }

    private fun observeCitySelection() {
        viewModelScope.launch {
            _uiState
                .map { it.selectedCity }
                .distinctUntilChanged()
                .collect { cityName ->
                    loadWeatherDataForCity(cityName)
                }
        }
    }

    private suspend fun loadWeatherDataForCity(cityName: String) {
        try {
            val weatherData = getWeatherDataUseCase.getWeatherByCity(cityName)
                ?: SampleWeatherData.generateRandomWeatherData(cityName)
            _previewWeatherData.value = weatherData
        } catch (e: Exception) {
            Log.e(TAG, "Error loading weather data for city: $cityName", e)
            val fallbackData = SampleWeatherData.generateRandomWeatherData(cityName)
            _previewWeatherData.value = fallbackData
        }
    }

    fun onCitySelected(cityName: String) {
        _uiState.value = _uiState.value.copy(selectedCity = cityName)
    }

    fun onIconSizeSelected(iconSize: IconSize) {
        _uiState.value = _uiState.value.copy(selectedIconSize = iconSize)
    }

    fun onFontSizeSelected(fontSize: FontSize) {
        _uiState.value = _uiState.value.copy(selectedFontSize = fontSize)
    }

    fun onShowHumidityToggled(show: Boolean) {
        _uiState.value = _uiState.value.copy(showHumidity = show)
    }

    fun onShowWindSpeedToggled(show: Boolean) {
        _uiState.value = _uiState.value.copy(showWindSpeed = show)
    }

    fun onBackgroundColorSelected(backgroundColor: BackgroundColor) {
        _uiState.value = _uiState.value.copy(selectedBackgroundColor = backgroundColor)
    }

    fun onTransparencyChanged(transparency: Float) {
        _uiState.value = _uiState.value.copy(transparency = transparency)
    }

    fun onIconSizeSliderChanged(value: Float) {
        val iconSize = when (value.toInt()) {
            0 -> IconSize.SMALL
            1 -> IconSize.MEDIUM
            2 -> IconSize.LARGE
            3 -> IconSize.EXTRA_LARGE
            else -> IconSize.MEDIUM
        }
        _uiState.value = _uiState.value.copy(selectedIconSize = iconSize)
    }

    fun onFontSizeSliderChanged(value: Float) {
        val fontSize = when (value.toInt()) {
            0 -> FontSize.SMALL
            1 -> FontSize.MEDIUM
            2 -> FontSize.LARGE
            3 -> FontSize.EXTRA_LARGE
            else -> FontSize.MEDIUM
        }
        _uiState.value = _uiState.value.copy(selectedFontSize = fontSize)
    }

    fun getIconSizeSliderValue(): Float {
        return when (_uiState.value.selectedIconSize) {
            IconSize.SMALL -> 0f
            IconSize.MEDIUM -> 1f
            IconSize.LARGE -> 2f
            IconSize.EXTRA_LARGE -> 3f
        }
    }

    fun getFontSizeSliderValue(): Float {
        return when (_uiState.value.selectedFontSize) {
            FontSize.SMALL -> 0f
            FontSize.MEDIUM -> 1f
            FontSize.LARGE -> 2f
            FontSize.EXTRA_LARGE -> 3f
        }
    }

    fun saveConfiguration() {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true)

                val currentState = _uiState.value
                val config = WidgetConfiguration(
                    widgetId = widgetId,
                    cityName = currentState.selectedCity,
                    iconSize = currentState.selectedIconSize,
                    fontSize = currentState.selectedFontSize,
                    backgroundColor = currentState.selectedBackgroundColor,
                    transparency = currentState.transparency,
                    showHumidity = currentState.showHumidity,
                    showWindSpeed = currentState.showWindSpeed
                )

                updateWidgetConfigUseCase.saveWidgetConfig(config)
                Log.d(TAG, "Widget configuration saved successfully")

                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    isSaved = true
                )
            } catch (e: Exception) {
                Log.e(TAG, "Error saving configuration", e)
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = e.message
                )
            }
        }
    }

    fun getCurrentConfiguration(): WidgetConfiguration {
        val currentState = _uiState.value
        return WidgetConfiguration(
            widgetId = widgetId,
            cityName = currentState.selectedCity,
            iconSize = currentState.selectedIconSize,
            fontSize = currentState.selectedFontSize,
            backgroundColor = currentState.selectedBackgroundColor,
            transparency = currentState.transparency,
            showHumidity = currentState.showHumidity,
            showWindSpeed = currentState.showWindSpeed
        )
    }
}

/**
 * UI State for widget configuration screen
 */
data class WidgetConfigUiState(
    val selectedCity: String = "",
    val selectedIconSize: IconSize = IconSize.MEDIUM,
    val selectedFontSize: FontSize = FontSize.MEDIUM,
    val selectedBackgroundColor: BackgroundColor = BackgroundColor.SYSTEM,
    val transparency: Float = 0.9f,
    val showHumidity: Boolean = true,
    val showWindSpeed: Boolean = true,
    val isLoading: Boolean = true,
    val isSaved: Boolean = false,
    val error: String? = null
)
