1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="io.despicable.chromeos.debug"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="30"
9        android:targetSdkVersion="36" />
10
11    <uses-permission android:name="android.permission.WAKE_LOCK" />
11-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:23:5-68
11-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:23:22-65
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:24:5-79
12-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:24:22-76
13    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
13-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:25:5-81
13-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:25:22-78
14    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
14-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:26:5-77
14-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:26:22-74
15
16    <permission
16-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee982a82fb86e03ba63249f20db390ab\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
17        android:name="io.despicable.chromeos.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
17-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee982a82fb86e03ba63249f20db390ab\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
18        android:protectionLevel="signature" />
18-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee982a82fb86e03ba63249f20db390ab\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
19
20    <uses-permission android:name="io.despicable.chromeos.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
20-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee982a82fb86e03ba63249f20db390ab\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
20-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee982a82fb86e03ba63249f20db390ab\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
21
22    <application
22-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:5:5-53:19
23        android:name="io.despicable.chromeos.WeatherApplication"
23-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:6:9-43
24        android:allowBackup="true"
24-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:7:9-35
25        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
25-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee982a82fb86e03ba63249f20db390ab\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
26        android:dataExtractionRules="@xml/data_extraction_rules"
26-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:8:9-65
27        android:debuggable="true"
28        android:extractNativeLibs="false"
29        android:fullBackupContent="@xml/backup_rules"
29-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:9:9-54
30        android:icon="@mipmap/ic_launcher"
30-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:10:9-43
31        android:label="@string/app_name"
31-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:11:9-41
32        android:roundIcon="@mipmap/ic_launcher_round"
32-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:12:9-54
33        android:supportsRtl="true"
33-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:13:9-35
34        android:theme="@style/Theme.ChromeOS" >
34-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:14:9-46
35        <activity
35-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:16:9-25:20
36            android:name="io.despicable.chromeos.MainActivity"
36-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:17:13-41
37            android:exported="true"
37-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:18:13-36
38            android:theme="@style/Theme.ChromeOS" >
38-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:19:13-50
39            <intent-filter>
39-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:20:13-24:29
40                <action android:name="android.intent.action.MAIN" />
40-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:21:17-69
40-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:21:25-66
41
42                <category android:name="android.intent.category.LAUNCHER" />
42-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:23:17-77
42-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:23:27-74
43            </intent-filter>
44        </activity>
45
46        <!-- Widget Configuration Activity -->
47        <activity
47-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:28:9-38:20
48            android:name="io.despicable.chromeos.presentation.ui.config.WidgetConfigActivity"
48-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:29:13-72
49            android:enabled="true"
49-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:30:13-35
50            android:exported="true"
50-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:31:13-36
51            android:imeOptions="actionSend|flagNoEnterAction"
51-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:32:13-62
52            android:permission="android.permission.UPDATE_WIDGETS" >
52-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:33:13-67
53            <intent-filter>
53-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:34:13-37:29
54                <action android:name="android.appwidget.action.APPWIDGET_CONFIGURE" />
54-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:35:17-87
54-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:35:25-84
55                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
55-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:36:17-84
55-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:36:25-81
56            </intent-filter>
57        </activity>
58
59        <!-- Weather Widget Provider -->
60        <receiver
60-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:41:9-52:20
61            android:name="io.despicable.chromeos.presentation.ui.widget.WeatherWidgetProvider"
61-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:42:13-73
62            android:exported="true" >
62-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:43:13-36
63            <intent-filter>
63-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:44:13-48:29
64                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
64-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:36:17-84
64-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:36:25-81
65                <action android:name="android.appwidget.action.APPWIDGET_DELETED" />
65-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:46:17-85
65-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:46:25-82
66                <action android:name="io.despicable.chromeos.ACTION_UPDATE_WEATHER" />
66-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:47:17-87
66-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:47:25-84
67            </intent-filter>
68
69            <meta-data
69-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:49:13-51:63
70                android:name="android.appwidget.provider"
70-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:50:17-58
71                android:resource="@xml/weather_widget_info" />
71-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:51:17-60
72        </receiver>
73
74        <activity
74-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:23:9-28:62
75            android:name="androidx.glance.appwidget.action.ActionTrampolineActivity"
75-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:24:13-85
76            android:enabled="true"
76-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:25:13-35
77            android:excludeFromRecents="true"
77-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:26:13-46
78            android:exported="false"
78-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:27:13-37
79            android:theme="@android:style/Theme.NoDisplay" />
79-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:28:13-59
80        <activity
80-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:29:9-36:81
81            android:name="androidx.glance.appwidget.action.InvisibleActionTrampolineActivity"
81-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:30:13-94
82            android:enabled="true"
82-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:31:13-35
83            android:exported="false"
83-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:32:13-37
84            android:launchMode="singleInstance"
84-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:33:13-48
85            android:noHistory="true"
85-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:34:13-37
86            android:taskAffinity="androidx.glance.appwidget.ListAdapterCallbackTrampoline"
86-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:35:13-91
87            android:theme="@style/Widget.Glance.AppWidget.CallbackTrampoline" />
87-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:36:13-78
88
89        <receiver
89-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:38:9-41:40
90            android:name="androidx.glance.appwidget.action.ActionCallbackBroadcastReceiver"
90-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:39:13-92
91            android:enabled="true"
91-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:40:13-35
92            android:exported="false" />
92-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:41:13-37
93        <receiver
93-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:42:9-45:40
94            android:name="androidx.glance.appwidget.UnmanagedSessionReceiver"
94-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:43:13-78
95            android:enabled="true"
95-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:44:13-35
96            android:exported="false" />
96-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:45:13-37
97        <receiver
97-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:46:9-53:20
98            android:name="androidx.glance.appwidget.MyPackageReplacedReceiver"
98-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:47:13-79
99            android:enabled="true"
99-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:48:13-35
100            android:exported="false" >
100-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:49:13-37
101            <intent-filter>
101-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:50:13-52:29
102                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
102-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:51:17-84
102-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:51:25-81
103            </intent-filter>
104        </receiver>
105
106        <service
106-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:55:9-58:72
107            android:name="androidx.glance.appwidget.GlanceRemoteViewsService"
107-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:56:13-78
108            android:exported="true"
108-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:57:13-36
109            android:permission="android.permission.BIND_REMOTEVIEWS" />
109-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:58:13-69
110
111        <activity
111-->[androidx.compose.ui:ui-tooling-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab7155561d762200be089d4877f3cdd6\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
112            android:name="androidx.compose.ui.tooling.PreviewActivity"
112-->[androidx.compose.ui:ui-tooling-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab7155561d762200be089d4877f3cdd6\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
113            android:exported="true" />
113-->[androidx.compose.ui:ui-tooling-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab7155561d762200be089d4877f3cdd6\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
114
115        <provider
115-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:29:9-37:20
116            android:name="androidx.startup.InitializationProvider"
116-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:30:13-67
117            android:authorities="io.despicable.chromeos.debug.androidx-startup"
117-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:31:13-68
118            android:exported="false" >
118-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:32:13-37
119            <meta-data
119-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:34:13-36:52
120                android:name="androidx.work.WorkManagerInitializer"
120-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:35:17-68
121                android:value="androidx.startup" />
121-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:36:17-49
122            <meta-data
122-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\81df52121eca3e8b1fdb816add69cc44\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
123                android:name="androidx.emoji2.text.EmojiCompatInitializer"
123-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\81df52121eca3e8b1fdb816add69cc44\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
124                android:value="androidx.startup" />
124-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\81df52121eca3e8b1fdb816add69cc44\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
125            <meta-data
125-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b94309e8c065092535228822469321bd\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:29:13-31:52
126                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
126-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b94309e8c065092535228822469321bd\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:30:17-78
127                android:value="androidx.startup" />
127-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b94309e8c065092535228822469321bd\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:31:17-49
128            <meta-data
128-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
129                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
129-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
130                android:value="androidx.startup" />
130-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
131        </provider>
132
133        <service
133-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:39:9-45:35
134            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
134-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:40:13-88
135            android:directBootAware="false"
135-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:41:13-44
136            android:enabled="@bool/enable_system_alarm_service_default"
136-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:42:13-72
137            android:exported="false" />
137-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:43:13-37
138        <service
138-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:46:9-52:35
139            android:name="androidx.work.impl.background.systemjob.SystemJobService"
139-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:47:13-84
140            android:directBootAware="false"
140-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:48:13-44
141            android:enabled="@bool/enable_system_job_service_default"
141-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:49:13-70
142            android:exported="true"
142-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:50:13-36
143            android:permission="android.permission.BIND_JOB_SERVICE" />
143-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:51:13-69
144        <service
144-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:53:9-59:35
145            android:name="androidx.work.impl.foreground.SystemForegroundService"
145-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:54:13-81
146            android:directBootAware="false"
146-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:55:13-44
147            android:enabled="@bool/enable_system_foreground_service_default"
147-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:56:13-77
148            android:exported="false" />
148-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:57:13-37
149
150        <receiver
150-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:61:9-66:35
151            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
151-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:62:13-88
152            android:directBootAware="false"
152-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:63:13-44
153            android:enabled="true"
153-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:64:13-35
154            android:exported="false" />
154-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:65:13-37
155        <receiver
155-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:67:9-77:20
156            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
156-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:68:13-106
157            android:directBootAware="false"
157-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:69:13-44
158            android:enabled="false"
158-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:70:13-36
159            android:exported="false" >
159-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:71:13-37
160            <intent-filter>
160-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:73:13-76:29
161                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
161-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:74:17-87
161-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:74:25-84
162                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
162-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:75:17-90
162-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:75:25-87
163            </intent-filter>
164        </receiver>
165        <receiver
165-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:78:9-88:20
166            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
166-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:79:13-104
167            android:directBootAware="false"
167-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:80:13-44
168            android:enabled="false"
168-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:81:13-36
169            android:exported="false" >
169-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:82:13-37
170            <intent-filter>
170-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:84:13-87:29
171                <action android:name="android.intent.action.BATTERY_OKAY" />
171-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:85:17-77
171-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:85:25-74
172                <action android:name="android.intent.action.BATTERY_LOW" />
172-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:86:17-76
172-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:86:25-73
173            </intent-filter>
174        </receiver>
175        <receiver
175-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:89:9-99:20
176            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
176-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:90:13-104
177            android:directBootAware="false"
177-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:91:13-44
178            android:enabled="false"
178-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:92:13-36
179            android:exported="false" >
179-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:93:13-37
180            <intent-filter>
180-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:95:13-98:29
181                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
181-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:96:17-83
181-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:96:25-80
182                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
182-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:97:17-82
182-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:97:25-79
183            </intent-filter>
184        </receiver>
185        <receiver
185-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:100:9-109:20
186            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
186-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:101:13-103
187            android:directBootAware="false"
187-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:102:13-44
188            android:enabled="false"
188-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:103:13-36
189            android:exported="false" >
189-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:104:13-37
190            <intent-filter>
190-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:106:13-108:29
191                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
191-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:107:17-79
191-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:107:25-76
192            </intent-filter>
193        </receiver>
194        <receiver
194-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:110:9-121:20
195            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
195-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:111:13-88
196            android:directBootAware="false"
196-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:112:13-44
197            android:enabled="false"
197-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:113:13-36
198            android:exported="false" >
198-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:114:13-37
199            <intent-filter>
199-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:116:13-120:29
200                <action android:name="android.intent.action.BOOT_COMPLETED" />
200-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:117:17-79
200-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:117:25-76
201                <action android:name="android.intent.action.TIME_SET" />
201-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:118:17-73
201-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:118:25-70
202                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
202-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:119:17-81
202-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:119:25-78
203            </intent-filter>
204        </receiver>
205        <receiver
205-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:122:9-131:20
206            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
206-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:123:13-99
207            android:directBootAware="false"
207-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:124:13-44
208            android:enabled="@bool/enable_system_alarm_service_default"
208-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:125:13-72
209            android:exported="false" >
209-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:126:13-37
210            <intent-filter>
210-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:128:13-130:29
211                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
211-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:129:17-98
211-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:129:25-95
212            </intent-filter>
213        </receiver>
214        <receiver
214-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:132:9-142:20
215            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
215-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:133:13-78
216            android:directBootAware="false"
216-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:134:13-44
217            android:enabled="true"
217-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:135:13-35
218            android:exported="true"
218-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:136:13-36
219            android:permission="android.permission.DUMP" >
219-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:137:13-57
220            <intent-filter>
220-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:139:13-141:29
221                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
221-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:140:17-88
221-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:140:25-85
222            </intent-filter>
223        </receiver>
224
225        <service
225-->[androidx.core:core-remoteviews:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dfcd53d77286ea764bdeb10a5454854d\transformed\core-remoteviews-1.1.0\AndroidManifest.xml:24:9-27:63
226            android:name="androidx.core.widget.RemoteViewsCompatService"
226-->[androidx.core:core-remoteviews:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dfcd53d77286ea764bdeb10a5454854d\transformed\core-remoteviews-1.1.0\AndroidManifest.xml:25:13-73
227            android:permission="android.permission.BIND_REMOTEVIEWS" />
227-->[androidx.core:core-remoteviews:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dfcd53d77286ea764bdeb10a5454854d\transformed\core-remoteviews-1.1.0\AndroidManifest.xml:26:13-69
228        <service
228-->[androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb70789ac6903ee3290ac157605813fc\transformed\room-runtime-release\AndroidManifest.xml:24:9-28:63
229            android:name="androidx.room.MultiInstanceInvalidationService"
229-->[androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb70789ac6903ee3290ac157605813fc\transformed\room-runtime-release\AndroidManifest.xml:25:13-74
230            android:directBootAware="true"
230-->[androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb70789ac6903ee3290ac157605813fc\transformed\room-runtime-release\AndroidManifest.xml:26:13-43
231            android:exported="false" />
231-->[androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb70789ac6903ee3290ac157605813fc\transformed\room-runtime-release\AndroidManifest.xml:27:13-37
232
233        <receiver
233-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
234            android:name="androidx.profileinstaller.ProfileInstallReceiver"
234-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
235            android:directBootAware="false"
235-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
236            android:enabled="true"
236-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
237            android:exported="true"
237-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
238            android:permission="android.permission.DUMP" >
238-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
239            <intent-filter>
239-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
240                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
240-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
240-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
241            </intent-filter>
242            <intent-filter>
242-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
243                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
243-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
243-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
244            </intent-filter>
245            <intent-filter>
245-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
246                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
246-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
246-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
247            </intent-filter>
248            <intent-filter>
248-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
249                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
249-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
249-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
250            </intent-filter>
251        </receiver>
252    </application>
253
254</manifest>
