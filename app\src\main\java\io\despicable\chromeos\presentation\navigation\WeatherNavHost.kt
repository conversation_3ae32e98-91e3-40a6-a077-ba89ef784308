package io.despicable.chromeos.presentation.navigation

import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.toRoute
import io.despicable.chromeos.presentation.ui.detail.WeatherDetailScreen
import io.despicable.chromeos.presentation.ui.home.HomeScreen

/**
 * Main navigation host for the weather app
 */
@Composable
fun WeatherNavHost(
    navController: NavHostController,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current

    // Handle widget navigation
    LaunchedEffect(Unit) {
        if (context is android.app.Activity) {
            val weatherId = context.intent.getLongExtra("weather_id", -1L)
            if (weatherId != -1L) {
                navController.navigate(WeatherRoutes.WeatherDetail(weatherId))
            }
        }
    }

    NavHost(
        navController = navController,
        startDestination = WeatherRoutes.Home,
        modifier = modifier
    ) {
        composable<WeatherRoutes.Home> {
            HomeScreen(
                onWeatherItemClick = { weatherId ->
                    navController.navigate(WeatherRoutes.WeatherDetail(weatherId))
                }
            )
        }

        composable<WeatherRoutes.WeatherDetail> { backStackEntry ->
            val weatherDetail = backStackEntry.toRoute<WeatherRoutes.WeatherDetail>()
            WeatherDetailScreen(
                onBackClick = {
                    navController.popBackStack()
                }
            )
        }
    }
}
