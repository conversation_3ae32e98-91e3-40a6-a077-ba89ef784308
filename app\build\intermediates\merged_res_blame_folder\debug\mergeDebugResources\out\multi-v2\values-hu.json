{"logs": [{"outputFile": "io.despicable.chromeos.app-mergeDebugResources-80:/values-hu/values-hu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d531cb9ac8d38fdf826cdbc25dccf69e\\transformed\\ui-release\\res\\values-hu\\values-hu.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "193,288,376,473,572,659,741,837,926,1013,1096,1184,1258,1351,1426,1497,1567,1646,1712", "endColumns": "94,87,96,98,86,81,95,88,86,82,87,73,92,74,70,69,78,65,120", "endOffsets": "283,371,468,567,654,736,832,921,1008,1091,1179,1253,1346,1421,1492,1562,1641,1707,1828"}, "to": {"startLines": "37,38,39,40,41,45,46,105,106,107,108,110,111,112,113,114,116,117,118", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3668,3763,3851,3948,4047,4585,4667,11190,11279,11366,11449,11621,11695,11788,11863,11934,12105,12184,12250", "endColumns": "94,87,96,98,86,81,95,88,86,82,87,73,92,74,70,69,78,65,120", "endOffsets": "3758,3846,3943,4042,4129,4662,4758,11274,11361,11444,11532,11690,11783,11858,11929,11999,12179,12245,12366"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2a5a5ac5787a9ad7f21a44018aa6dab8\\transformed\\appcompat-1.7.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,305,420,504,619,742,819,894,985,1078,1173,1267,1367,1460,1555,1650,1741,1832,1915,2025,2135,2235,2346,2455,2574,2756,2859", "endColumns": "107,91,114,83,114,122,76,74,90,92,94,93,99,92,94,94,90,90,82,109,109,99,110,108,118,181,102,83", "endOffsets": "208,300,415,499,614,737,814,889,980,1073,1168,1262,1362,1455,1550,1645,1736,1827,1910,2020,2130,2230,2341,2450,2569,2751,2854,2938"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,109", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,305,420,504,619,742,819,894,985,1078,1173,1267,1367,1460,1555,1650,1741,1832,1915,2025,2135,2235,2346,2455,2574,2756,11537", "endColumns": "107,91,114,83,114,122,76,74,90,92,94,93,99,92,94,94,90,90,82,109,109,99,110,108,118,181,102,83", "endOffsets": "208,300,415,499,614,737,814,889,980,1073,1168,1262,1362,1455,1550,1645,1736,1827,1910,2020,2130,2230,2341,2450,2569,2751,2854,11616"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\834bf615f3749af516fa6fc397086833\\transformed\\material-release\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "96", "endOffsets": "147"}, "to": {"startLines": "104", "startColumns": "4", "startOffsets": "11093", "endColumns": "96", "endOffsets": "11185"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\0f1e3221b145c9b74bd642a03e5d8a29\\transformed\\glance-appwidget-1.1.1\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,265,382", "endColumns": "209,116,123", "endOffsets": "260,377,501"}, "to": {"startLines": "42,43,44", "startColumns": "4,4,4", "startOffsets": "4134,4344,4461", "endColumns": "209,116,123", "endOffsets": "4339,4456,4580"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\73e1d3faf204d8dc115e2ef9096d86cb\\transformed\\foundation-release\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,142,231", "endColumns": "86,88,96", "endOffsets": "137,226,323"}, "to": {"startLines": "29,119,120", "startColumns": "4,4,4", "startOffsets": "2859,12371,12460", "endColumns": "86,88,96", "endOffsets": "2941,12455,12552"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ee982a82fb86e03ba63249f20db390ab\\transformed\\core-1.16.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,356,457,560,667,777", "endColumns": "96,101,101,100,102,106,109,100", "endOffsets": "147,249,351,452,555,662,772,873"}, "to": {"startLines": "30,31,32,33,34,35,36,115", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2946,3043,3145,3247,3348,3451,3558,12004", "endColumns": "96,101,101,100,102,106,109,100", "endOffsets": "3038,3140,3242,3343,3446,3553,3663,12100"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c91fc286a144f7dd3fed97bfa6a44d86\\transformed\\material3-release\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,176,292,400,516,611,708,822,962,1085,1232,1317,1417,1515,1617,1739,1876,1981,2121,2259,2385,2581,2704,2826,2948,3074,3173,3268,3387,3524,3626,3737,3841,3986,4133,4240,4347,4431,4529,4623,4731,4819,4906,5007,5088,5171,5270,5376,5471,5574,5660,5769,5867,5973,6094,6175,6287", "endColumns": "120,115,107,115,94,96,113,139,122,146,84,99,97,101,121,136,104,139,137,125,195,122,121,121,125,98,94,118,136,101,110,103,144,146,106,106,83,97,93,107,87,86,100,80,82,98,105,94,102,85,108,97,105,120,80,111,97", "endOffsets": "171,287,395,511,606,703,817,957,1080,1227,1312,1412,1510,1612,1734,1871,1976,2116,2254,2380,2576,2699,2821,2943,3069,3168,3263,3382,3519,3621,3732,3836,3981,4128,4235,4342,4426,4524,4618,4726,4814,4901,5002,5083,5166,5265,5371,5466,5569,5655,5764,5862,5968,6089,6170,6282,6380"}, "to": {"startLines": "47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4763,4884,5000,5108,5224,5319,5416,5530,5670,5793,5940,6025,6125,6223,6325,6447,6584,6689,6829,6967,7093,7289,7412,7534,7656,7782,7881,7976,8095,8232,8334,8445,8549,8694,8841,8948,9055,9139,9237,9331,9439,9527,9614,9715,9796,9879,9978,10084,10179,10282,10368,10477,10575,10681,10802,10883,10995", "endColumns": "120,115,107,115,94,96,113,139,122,146,84,99,97,101,121,136,104,139,137,125,195,122,121,121,125,98,94,118,136,101,110,103,144,146,106,106,83,97,93,107,87,86,100,80,82,98,105,94,102,85,108,97,105,120,80,111,97", "endOffsets": "4879,4995,5103,5219,5314,5411,5525,5665,5788,5935,6020,6120,6218,6320,6442,6579,6684,6824,6962,7088,7284,7407,7529,7651,7777,7876,7971,8090,8227,8329,8440,8544,8689,8836,8943,9050,9134,9232,9326,9434,9522,9609,9710,9791,9874,9973,10079,10174,10277,10363,10472,10570,10676,10797,10878,10990,11088"}}]}]}