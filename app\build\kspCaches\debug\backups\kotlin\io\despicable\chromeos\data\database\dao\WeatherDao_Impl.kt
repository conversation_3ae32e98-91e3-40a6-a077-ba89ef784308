package io.despicable.chromeos.`data`.database.dao

import androidx.room.EntityInsertAdapter
import androidx.room.RoomDatabase
import androidx.room.coroutines.createFlow
import androidx.room.util.getColumnIndexOrThrow
import androidx.room.util.performSuspending
import androidx.sqlite.SQLiteStatement
import io.despicable.chromeos.`data`.database.entity.WeatherEntity
import javax.`annotation`.processing.Generated
import kotlin.Double
import kotlin.Int
import kotlin.Long
import kotlin.String
import kotlin.Suppress
import kotlin.Unit
import kotlin.collections.List
import kotlin.collections.MutableList
import kotlin.collections.mutableListOf
import kotlin.reflect.KClass
import kotlinx.coroutines.flow.Flow

@Generated(value = ["androidx.room.RoomProcessor"])
@Suppress(names = ["UNCHECKED_CAST", "DEPRECATION", "REDUNDANT_PROJECTION", "REMOVAL"])
public class WeatherDao_Impl(
  __db: RoomDatabase,
) : WeatherDao {
  private val __db: RoomDatabase

  private val __insertAdapterOfWeatherEntity: EntityInsertAdapter<WeatherEntity>
  init {
    this.__db = __db
    this.__insertAdapterOfWeatherEntity = object : EntityInsertAdapter<WeatherEntity>() {
      protected override fun createQuery(): String =
          "INSERT OR REPLACE INTO `weather_data` (`id`,`cityName`,`temperature`,`weatherCondition`,`humidity`,`windSpeed`,`lastUpdated`) VALUES (nullif(?, 0),?,?,?,?,?,?)"

      protected override fun bind(statement: SQLiteStatement, entity: WeatherEntity) {
        statement.bindLong(1, entity.id)
        statement.bindText(2, entity.cityName)
        statement.bindLong(3, entity.temperature.toLong())
        statement.bindText(4, entity.weatherCondition)
        statement.bindLong(5, entity.humidity.toLong())
        statement.bindDouble(6, entity.windSpeed)
        statement.bindLong(7, entity.lastUpdated)
      }
    }
  }

  public override suspend fun insertWeatherData(weatherEntity: WeatherEntity): Unit =
      performSuspending(__db, false, true) { _connection ->
    __insertAdapterOfWeatherEntity.insert(_connection, weatherEntity)
  }

  public override suspend fun insertAllWeatherData(weatherEntities: List<WeatherEntity>): Unit =
      performSuspending(__db, false, true) { _connection ->
    __insertAdapterOfWeatherEntity.insert(_connection, weatherEntities)
  }

  public override suspend fun getWeatherByCity(cityName: String): WeatherEntity? {
    val _sql: String = "SELECT * FROM weather_data WHERE cityName = ? LIMIT 1"
    return performSuspending(__db, true, false) { _connection ->
      val _stmt: SQLiteStatement = _connection.prepare(_sql)
      try {
        var _argIndex: Int = 1
        _stmt.bindText(_argIndex, cityName)
        val _columnIndexOfId: Int = getColumnIndexOrThrow(_stmt, "id")
        val _columnIndexOfCityName: Int = getColumnIndexOrThrow(_stmt, "cityName")
        val _columnIndexOfTemperature: Int = getColumnIndexOrThrow(_stmt, "temperature")
        val _columnIndexOfWeatherCondition: Int = getColumnIndexOrThrow(_stmt, "weatherCondition")
        val _columnIndexOfHumidity: Int = getColumnIndexOrThrow(_stmt, "humidity")
        val _columnIndexOfWindSpeed: Int = getColumnIndexOrThrow(_stmt, "windSpeed")
        val _columnIndexOfLastUpdated: Int = getColumnIndexOrThrow(_stmt, "lastUpdated")
        val _result: WeatherEntity?
        if (_stmt.step()) {
          val _tmpId: Long
          _tmpId = _stmt.getLong(_columnIndexOfId)
          val _tmpCityName: String
          _tmpCityName = _stmt.getText(_columnIndexOfCityName)
          val _tmpTemperature: Int
          _tmpTemperature = _stmt.getLong(_columnIndexOfTemperature).toInt()
          val _tmpWeatherCondition: String
          _tmpWeatherCondition = _stmt.getText(_columnIndexOfWeatherCondition)
          val _tmpHumidity: Int
          _tmpHumidity = _stmt.getLong(_columnIndexOfHumidity).toInt()
          val _tmpWindSpeed: Double
          _tmpWindSpeed = _stmt.getDouble(_columnIndexOfWindSpeed)
          val _tmpLastUpdated: Long
          _tmpLastUpdated = _stmt.getLong(_columnIndexOfLastUpdated)
          _result =
              WeatherEntity(_tmpId,_tmpCityName,_tmpTemperature,_tmpWeatherCondition,_tmpHumidity,_tmpWindSpeed,_tmpLastUpdated)
        } else {
          _result = null
        }
        _result
      } finally {
        _stmt.close()
      }
    }
  }

  public override fun getAllCities(): Flow<List<String>> {
    val _sql: String = "SELECT DISTINCT cityName FROM weather_data ORDER BY cityName ASC"
    return createFlow(__db, false, arrayOf("weather_data")) { _connection ->
      val _stmt: SQLiteStatement = _connection.prepare(_sql)
      try {
        val _result: MutableList<String> = mutableListOf()
        while (_stmt.step()) {
          val _item: String
          _item = _stmt.getText(0)
          _result.add(_item)
        }
        _result
      } finally {
        _stmt.close()
      }
    }
  }

  public override fun getAllWeatherData(): Flow<List<WeatherEntity>> {
    val _sql: String = "SELECT * FROM weather_data ORDER BY lastUpdated DESC"
    return createFlow(__db, false, arrayOf("weather_data")) { _connection ->
      val _stmt: SQLiteStatement = _connection.prepare(_sql)
      try {
        val _columnIndexOfId: Int = getColumnIndexOrThrow(_stmt, "id")
        val _columnIndexOfCityName: Int = getColumnIndexOrThrow(_stmt, "cityName")
        val _columnIndexOfTemperature: Int = getColumnIndexOrThrow(_stmt, "temperature")
        val _columnIndexOfWeatherCondition: Int = getColumnIndexOrThrow(_stmt, "weatherCondition")
        val _columnIndexOfHumidity: Int = getColumnIndexOrThrow(_stmt, "humidity")
        val _columnIndexOfWindSpeed: Int = getColumnIndexOrThrow(_stmt, "windSpeed")
        val _columnIndexOfLastUpdated: Int = getColumnIndexOrThrow(_stmt, "lastUpdated")
        val _result: MutableList<WeatherEntity> = mutableListOf()
        while (_stmt.step()) {
          val _item: WeatherEntity
          val _tmpId: Long
          _tmpId = _stmt.getLong(_columnIndexOfId)
          val _tmpCityName: String
          _tmpCityName = _stmt.getText(_columnIndexOfCityName)
          val _tmpTemperature: Int
          _tmpTemperature = _stmt.getLong(_columnIndexOfTemperature).toInt()
          val _tmpWeatherCondition: String
          _tmpWeatherCondition = _stmt.getText(_columnIndexOfWeatherCondition)
          val _tmpHumidity: Int
          _tmpHumidity = _stmt.getLong(_columnIndexOfHumidity).toInt()
          val _tmpWindSpeed: Double
          _tmpWindSpeed = _stmt.getDouble(_columnIndexOfWindSpeed)
          val _tmpLastUpdated: Long
          _tmpLastUpdated = _stmt.getLong(_columnIndexOfLastUpdated)
          _item =
              WeatherEntity(_tmpId,_tmpCityName,_tmpTemperature,_tmpWeatherCondition,_tmpHumidity,_tmpWindSpeed,_tmpLastUpdated)
          _result.add(_item)
        }
        _result
      } finally {
        _stmt.close()
      }
    }
  }

  public override suspend fun getWeatherDataCount(): Int {
    val _sql: String = "SELECT COUNT(*) FROM weather_data"
    return performSuspending(__db, true, false) { _connection ->
      val _stmt: SQLiteStatement = _connection.prepare(_sql)
      try {
        val _result: Int
        if (_stmt.step()) {
          val _tmp: Int
          _tmp = _stmt.getLong(0).toInt()
          _result = _tmp
        } else {
          _result = 0
        }
        _result
      } finally {
        _stmt.close()
      }
    }
  }

  public override suspend fun deleteWeatherByCity(cityName: String) {
    val _sql: String = "DELETE FROM weather_data WHERE cityName = ?"
    return performSuspending(__db, false, true) { _connection ->
      val _stmt: SQLiteStatement = _connection.prepare(_sql)
      try {
        var _argIndex: Int = 1
        _stmt.bindText(_argIndex, cityName)
        _stmt.step()
      } finally {
        _stmt.close()
      }
    }
  }

  public override suspend fun deleteAllWeatherData() {
    val _sql: String = "DELETE FROM weather_data"
    return performSuspending(__db, false, true) { _connection ->
      val _stmt: SQLiteStatement = _connection.prepare(_sql)
      try {
        _stmt.step()
      } finally {
        _stmt.close()
      }
    }
  }

  public companion object {
    public fun getRequiredConverters(): List<KClass<*>> = emptyList()
  }
}
