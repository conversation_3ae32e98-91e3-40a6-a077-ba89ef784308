{"logs": [{"outputFile": "io.despicable.chromeos.app-mergeDebugResources-80:/values-hr/values-hr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ee982a82fb86e03ba63249f20db390ab\\transformed\\core-1.16.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,260,357,456,560,664,781", "endColumns": "97,106,96,98,103,103,116,100", "endOffsets": "148,255,352,451,555,659,776,877"}, "to": {"startLines": "30,31,32,33,34,35,36,115", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2906,3004,3111,3208,3307,3411,3515,11955", "endColumns": "97,106,96,98,103,103,116,100", "endOffsets": "2999,3106,3203,3302,3406,3510,3627,12051"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c91fc286a144f7dd3fed97bfa6a44d86\\transformed\\material3-release\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,175,296,416,537,637,731,842,983,1102,1247,1332,1432,1527,1625,1744,1870,1975,2111,2246,2380,2548,2674,2798,2926,3050,3146,3244,3374,3508,3605,3707,3816,3957,4104,4213,4313,4398,4491,4586,4699,4793,4879,4988,5076,5159,5256,5357,5450,5547,5635,5743,5840,5942,6080,6170,6270", "endColumns": "119,120,119,120,99,93,110,140,118,144,84,99,94,97,118,125,104,135,134,133,167,125,123,127,123,95,97,129,133,96,101,108,140,146,108,99,84,92,94,112,93,85,108,87,82,96,100,92,96,87,107,96,101,137,89,99,91", "endOffsets": "170,291,411,532,632,726,837,978,1097,1242,1327,1427,1522,1620,1739,1865,1970,2106,2241,2375,2543,2669,2793,2921,3045,3141,3239,3369,3503,3600,3702,3811,3952,4099,4208,4308,4393,4486,4581,4694,4788,4874,4983,5071,5154,5251,5352,5445,5542,5630,5738,5835,5937,6075,6165,6265,6357"}, "to": {"startLines": "47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4728,4848,4969,5089,5210,5310,5404,5515,5656,5775,5920,6005,6105,6200,6298,6417,6543,6648,6784,6919,7053,7221,7347,7471,7599,7723,7819,7917,8047,8181,8278,8380,8489,8630,8777,8886,8986,9071,9164,9259,9372,9466,9552,9661,9749,9832,9929,10030,10123,10220,10308,10416,10513,10615,10753,10843,10943", "endColumns": "119,120,119,120,99,93,110,140,118,144,84,99,94,97,118,125,104,135,134,133,167,125,123,127,123,95,97,129,133,96,101,108,140,146,108,99,84,92,94,112,93,85,108,87,82,96,100,92,96,87,107,96,101,137,89,99,91", "endOffsets": "4843,4964,5084,5205,5305,5399,5510,5651,5770,5915,6000,6100,6195,6293,6412,6538,6643,6779,6914,7048,7216,7342,7466,7594,7718,7814,7912,8042,8176,8273,8375,8484,8625,8772,8881,8981,9066,9159,9254,9367,9461,9547,9656,9744,9827,9924,10025,10118,10215,10303,10411,10508,10610,10748,10838,10938,11030"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\0f1e3221b145c9b74bd642a03e5d8a29\\transformed\\glance-appwidget-1.1.1\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,255,370", "endColumns": "199,114,133", "endOffsets": "250,365,499"}, "to": {"startLines": "42,43,44", "startColumns": "4,4,4", "startOffsets": "4107,4307,4422", "endColumns": "199,114,133", "endOffsets": "4302,4417,4551"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2a5a5ac5787a9ad7f21a44018aa6dab8\\transformed\\appcompat-1.7.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,305,412,498,602,721,806,888,979,1072,1167,1261,1361,1454,1549,1644,1735,1826,1912,2016,2128,2229,2334,2448,2550,2719,2816", "endColumns": "104,94,106,85,103,118,84,81,90,92,94,93,99,92,94,94,90,90,85,103,111,100,104,113,101,168,96,84", "endOffsets": "205,300,407,493,597,716,801,883,974,1067,1162,1256,1356,1449,1544,1639,1730,1821,1907,2011,2123,2224,2329,2443,2545,2714,2811,2896"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,109", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,305,412,498,602,721,806,888,979,1072,1167,1261,1361,1454,1549,1644,1735,1826,1912,2016,2128,2229,2334,2448,2550,2719,11475", "endColumns": "104,94,106,85,103,118,84,81,90,92,94,93,99,92,94,94,90,90,85,103,111,100,104,113,101,168,96,84", "endOffsets": "205,300,407,493,597,716,801,883,974,1067,1162,1256,1356,1449,1544,1639,1730,1821,1907,2011,2123,2224,2329,2443,2545,2714,2811,11555"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\73e1d3faf204d8dc115e2ef9096d86cb\\transformed\\foundation-release\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,145,228", "endColumns": "89,82,84", "endOffsets": "140,223,308"}, "to": {"startLines": "29,119,120", "startColumns": "4,4,4", "startOffsets": "2816,12323,12406", "endColumns": "89,82,84", "endOffsets": "2901,12401,12486"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d531cb9ac8d38fdf826cdbc25dccf69e\\transformed\\ui-release\\res\\values-hr\\values-hr.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "196,301,388,482,581,671,750,843,938,1023,1104,1190,1263,1352,1429,1508,1585,1664,1734", "endColumns": "104,86,93,98,89,78,92,94,84,80,85,72,88,76,78,76,78,69,117", "endOffsets": "296,383,477,576,666,745,838,933,1018,1099,1185,1258,1347,1424,1503,1580,1659,1729,1847"}, "to": {"startLines": "37,38,39,40,41,45,46,105,106,107,108,110,111,112,113,114,116,117,118", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3632,3737,3824,3918,4017,4556,4635,11128,11223,11308,11389,11560,11633,11722,11799,11878,12056,12135,12205", "endColumns": "104,86,93,98,89,78,92,94,84,80,85,72,88,76,78,76,78,69,117", "endOffsets": "3732,3819,3913,4012,4102,4630,4723,11218,11303,11384,11470,11628,11717,11794,11873,11950,12130,12200,12318"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\834bf615f3749af516fa6fc397086833\\transformed\\material-release\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "92", "endOffsets": "143"}, "to": {"startLines": "104", "startColumns": "4", "startOffsets": "11035", "endColumns": "92", "endOffsets": "11123"}}]}]}