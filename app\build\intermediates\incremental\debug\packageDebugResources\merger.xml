<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\res"><file name="ic_launcher_background" path="C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="weather_widget_preview" path="C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\res\drawable\weather_widget_preview.xml" qualifiers="" type="drawable"/><file name="widget_background" path="C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\res\drawable\widget_background.xml" qualifiers="" type="drawable"/><file name="weather_widget_loading" path="C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\res\layout\weather_widget_loading.xml" qualifiers="" type="layout"/><file name="ic_launcher" path="C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\res\mipmap-anydpi\ic_launcher.xml" qualifiers="anydpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\res\mipmap-anydpi\ic_launcher_round.xml" qualifiers="anydpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\res\values\colors.xml" qualifiers=""><color name="purple_200">#FFBB86FC</color><color name="purple_500">#FF6200EE</color><color name="purple_700">#FF3700B3</color><color name="teal_200">#FF03DAC5</color><color name="teal_700">#FF018786</color><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color></file><file path="C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">chromeOS</string><string name="weather_widget_description">Weather widget with customizable display options</string><string name="loading_weather">Loading weather…</string><string name="configure_widget">Configure Weather Widget</string><string name="select_city">Select City</string><string name="icon_size">Icon Size</string><string name="font_size">Font Size</string><string name="additional_info">Additional Information</string><string name="show_humidity">Show Humidity</string><string name="show_wind_speed">Show Wind Speed</string><string name="save_configuration">Save Widget Configuration</string><string name="preview">Preview</string></file><file path="C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.ChromeOS" parent="android:Theme.Material.Light.NoActionBar"/></file><file name="backup_rules" path="C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="weather_widget_info" path="C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\res\xml\weather_widget_info.xml" qualifiers="" type="xml"/><file name="weather_widget_info" path="C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\res\xml-v31\weather_widget_info.xml" qualifiers="v31" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\STUDIO\chromeOS_2\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\STUDIO\chromeOS_2\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\STUDIO\chromeOS_2\app\build\generated\res\resValues\debug"/><source path="C:\Users\<USER>\STUDIO\chromeOS_2\app\build\generated\aboutLibraries\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\STUDIO\chromeOS_2\app\build\generated\res\resValues\debug"/><source path="C:\Users\<USER>\STUDIO\chromeOS_2\app\build\generated\aboutLibraries\debug\res"><file name="aboutlibraries" path="C:\Users\<USER>\STUDIO\chromeOS_2\app\build\generated\aboutLibraries\debug\res\raw\aboutlibraries.json" qualifiers="" type="raw"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="legacy_api_res$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="legacy_api_res" generated-set="legacy_api_res$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><mergedItems/></merger>