/ Header Record For PersistentHashMapValueStorage5 4io.despicable.chromeos.data.database.WeatherDatabase4 3io.despicable.chromeos.data.database.dao.WeatherDao$ #androidx.activity.ComponentActivity android.app.Application androidx.room.RoomDatabase; :io.despicable.chromeos.domain.repository.WeatherRepository@ ?io.despicable.chromeos.domain.repository.WidgetConfigRepository3 2kotlinx.serialization.internal.GeneratedSerializer kotlin.Enum3 2kotlinx.serialization.internal.GeneratedSerializer kotlin.Enum kotlin.Enum= <io.despicable.chromeos.presentation.navigation.WeatherRoutes= <io.despicable.chromeos.presentation.navigation.WeatherRoutes3 2kotlinx.serialization.internal.GeneratedSerializer$ #androidx.activity.ComponentActivity* )androidx.glance.appwidget.GlanceAppWidget2 1androidx.glance.appwidget.GlanceAppWidgetReceiver androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel androidx.lifecycle.ViewModelD androidx.work.CoroutineWorker%org.koin.core.component.KoinComponent; :io.despicable.chromeos.domain.repository.WeatherRepository androidx.lifecycle.ViewModel kotlin.Enum$ #androidx.activity.ComponentActivity3 2kotlinx.serialization.internal.GeneratedSerializer kotlin.Enum kotlin.Enum* )androidx.glance.appwidget.GlanceAppWidget androidx.lifecycle.ViewModel@ ?io.despicable.chromeos.domain.repository.WidgetConfigRepository