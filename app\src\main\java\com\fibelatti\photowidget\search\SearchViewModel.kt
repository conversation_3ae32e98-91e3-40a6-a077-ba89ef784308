package com.fibelatti.photowidget.search

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.collections.immutable.toImmutableList
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel for managing search functionality
 */
@HiltViewModel
class SearchViewModel @Inject constructor(
    private val searchRepository: SearchRepository,
) : ViewModel() {

    private val _uiState = MutableStateFlow(SearchUiState())
    val uiState: StateFlow<SearchUiState> = _uiState.asStateFlow()

    init {
        setupSearchDebouncing()
    }

    /**
     * Updates the search query
     */
    fun updateSearchQuery(query: String) {
        _uiState.update { it.copy(searchQuery = query, error = null) }
    }

    /**
     * Clears the search query and results
     */
    fun clearSearch() {
        _uiState.update {
            it.copy(
                searchQuery = "",
                searchResults = it.searchResults.clear(),
                showEmptyState = false,
                error = null
            )
        }
    }

    /**
     * Shows or hides the search bar
     */
    fun setSearchBarVisible(visible: Boolean) {
        _uiState.update { it.copy(isSearchBarVisible = visible) }
        if (!visible) {
            clearSearch()
        }
    }

    /**
     * Performs the actual search operation
     */
    private fun performSearch(query: String) {
        if (query.isBlank()) {
            _uiState.update {
                it.copy(
                    searchResults = it.searchResults.clear(),
                    isSearching = false,
                    showEmptyState = false
                )
            }
            return
        }

        viewModelScope.launch {
            _uiState.update { it.copy(isSearching = true, error = null) }

            try {
                // Simulate search operation - replace with actual search logic
                val results = searchContent(query)

                _uiState.update {
                    it.copy(
                        searchResults = results.toImmutableList(),
                        isSearching = false,
                        showEmptyState = results.isEmpty()
                    )
                }
            } catch (e: Exception) {
                _uiState.update {
                    it.copy(
                        isSearching = false,
                        error = e.message ?: "Search failed"
                    )
                }
            }
        }
    }

    /**
     * Sets up debounced search to avoid excessive API calls
     */
    @OptIn(FlowPreview::class)
    private fun setupSearchDebouncing() {
        _uiState
            .distinctUntilChanged { old, new -> old.searchQuery == new.searchQuery }
            .debounce(300) // 300ms debounce
            .onEach { state ->
                performSearch(state.searchQuery)
            }
            .launchIn(viewModelScope)
    }

    /**
     * Mock search implementation - replace with actual search logic
     */
    private suspend fun searchContent(query: String): List<SearchResult> {
        // Simulate network delay
        kotlinx.coroutines.delay(500)

        // Mock search results
        return listOf(
            SearchResult(
                id = "1",
                title = "Photo: $query",
                subtitle = "Found in gallery",
                type = SearchResultType.PHOTO
            ),
            SearchResult(
                id = "2",
                title = "Location: $query",
                subtitle = "Saved location",
                type = SearchResultType.LOCATION
            ),
            SearchResult(
                id = "3",
                title = "Widget: $query",
                subtitle = "Photo widget",
                type = SearchResultType.WIDGET
            )
        ).filter { it.title.contains(query, ignoreCase = true) }
    }

    /**
     * Handles search result selection
     */
    fun onSearchResultSelected(result: SearchResult) {
        // Handle result selection based on type
        when (result.type) {
            SearchResultType.PHOTO -> {
                // Navigate to photo or select photo
            }
            SearchResultType.LOCATION -> {
                // Navigate to location or select location
            }
            SearchResultType.WIDGET -> {
                // Navigate to widget configuration
            }
            SearchResultType.FOLDER -> {
                // Navigate to folder
            }
        }
    }
}
