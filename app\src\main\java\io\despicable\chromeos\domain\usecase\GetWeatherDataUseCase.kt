package io.despicable.chromeos.domain.usecase

import io.despicable.chromeos.domain.model.WeatherData
import io.despicable.chromeos.domain.repository.WeatherRepository
import kotlinx.coroutines.flow.Flow
/**
 * Use case for retrieving weather data
 */
class GetWeatherDataUseCase(
    private val weatherRepository: WeatherRepository
) {

    /**
     * Get weather data for a specific city
     */
    suspend fun getWeatherByCity(cityName: String): WeatherData? {
        return weatherRepository.getWeatherByCity(cityName)
    }

    /**
     * Get all available cities
     */
    fun getAllCities(): Flow<List<String>> {
        return weatherRepository.getAllCities()
    }

    /**
     * Get all weather data
     */
    fun getAllWeatherData(): Flow<List<WeatherData>> {
        return weatherRepository.getAllWeatherData()
    }

    /**
     * Refresh weather data
     */
    suspend fun refreshWeatherData() {
        weatherRepository.refreshWeatherData()
    }
}
