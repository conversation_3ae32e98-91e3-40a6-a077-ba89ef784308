package io.despicable.chromeos.presentation.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import io.despicable.chromeos.domain.model.WeatherData
import io.despicable.chromeos.domain.usecase.GetWeatherDataUseCase
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.launch
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.toImmutableList

/**
 * ViewModel for the Home screen managing weather list state
 */
class HomeViewModel(
    private val getWeatherDataUseCase: GetWeatherDataUseCase
) : ViewModel() {

    private val _uiState = MutableStateFlow(HomeUiState())
    val uiState: StateFlow<HomeUiState> = _uiState.asStateFlow()

    init {
        loadWeatherData()
    }

    /**
     * Load weather data from repository
     */
    fun loadWeatherData() {
        viewModelScope.launch {
            getWeatherDataUseCase.getAllWeatherData()
                .onStart {
                    _uiState.value = _uiState.value.copy(isLoading = true, error = null)
                }
                .catch { exception ->
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = exception.message ?: "Unknown error occurred"
                    )
                }
                .collect { weatherList ->
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        weatherList = weatherList.toImmutableList(),
                        error = null
                    )
                }
        }
    }

    /**
     * Refresh weather data
     */
    fun refreshWeatherData() {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isRefreshing = true)
                getWeatherDataUseCase.refreshWeatherData()
                // Reload data after refresh
                loadWeatherData()
            } catch (exception: Exception) {
                _uiState.value = _uiState.value.copy(
                    isRefreshing = false,
                    error = exception.message ?: "Failed to refresh weather data"
                )
            } finally {
                _uiState.value = _uiState.value.copy(isRefreshing = false)
            }
        }
    }

    /**
     * Clear error state
     */
    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }
}

/**
 * UI state for the Home screen
 */
data class HomeUiState(
    val isLoading: Boolean = false,
    val isRefreshing: Boolean = false,
    val weatherList: ImmutableList<WeatherData> = kotlinx.collections.immutable.persistentListOf(),
    val error: String? = null
) {
    val isEmpty: Boolean
        get() = weatherList.isEmpty() && !isLoading
}
