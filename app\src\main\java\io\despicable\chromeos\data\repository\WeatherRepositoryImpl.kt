package io.despicable.chromeos.data.repository

import android.util.Log
import io.despicable.chromeos.data.database.dao.WeatherDao
import io.despicable.chromeos.data.database.entity.toEntity
import io.despicable.chromeos.data.database.entity.toDomainModel
import io.despicable.chromeos.domain.model.WeatherData
import io.despicable.chromeos.domain.repository.WeatherRepository
import io.despicable.chromeos.util.SampleWeatherData
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.runBlocking
/**
 * Implementation of WeatherRepository using Room database
 */
class WeatherRepositoryImpl(
    private val weatherDao: WeatherDao
) : WeatherRepository {

    companion object {
        private const val TAG = "WeatherRepositoryImpl"
    }

    override suspend fun getWeatherByCity(cityName: String): WeatherData? {
        return weatherDao.getWeatherByCity(cityName)?.toDomainModel()
    }

    override fun getAllCities(): Flow<List<String>> {
        return weatherDao.getAllCities()
    }

    override fun getAllWeatherData(): Flow<List<WeatherData>> {
        return weatherDao.getAllWeatherData().map { entities ->
            // If database is empty, populate with sample data
            if (entities.isEmpty()) {
                Log.d(TAG, "Database is empty, populating with sample data")
                val sampleData = SampleWeatherData.getSampleWeatherData()
                // Insert sample data asynchronously
                kotlinx.coroutines.runBlocking {
                    weatherDao.insertAllWeatherData(sampleData.map { it.toEntity() })
                }
                // Return sample data immediately
                sampleData
            } else {
                entities.map { it.toDomainModel() }
            }
        }
    }

    override suspend fun insertWeatherData(weatherData: WeatherData) {
        weatherDao.insertWeatherData(weatherData.toEntity())
    }

    override suspend fun deleteWeatherData(cityName: String) {
        weatherDao.deleteWeatherByCity(cityName)
    }

    override suspend fun refreshWeatherData() {
        // Check if database is empty and populate with sample data
        val count = weatherDao.getWeatherDataCount()
        if (count == 0) {
            val sampleData = SampleWeatherData.getSampleWeatherData()
            weatherDao.insertAllWeatherData(sampleData.map { it.toEntity() })
        }
        // In a real app, this would make API calls to refresh data
    }
}
