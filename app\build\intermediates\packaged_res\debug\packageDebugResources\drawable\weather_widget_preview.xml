<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="200dp"
    android:height="160dp"
    android:viewportWidth="200"
    android:viewportHeight="160">

    <!-- Background -->
    <path
        android:fillColor="#FFFFFF"
        android:pathData="M16,0 L184,0 Q200,0 200,16 L200,144 Q200,160 184,160 L16,160 Q0,160 0,144 L0,16 Q0,0 16,0 Z" />

    <!-- Border -->
    <path
        android:fillColor="#00000000"
        android:pathData="M16,1 L184,1 Q199,1 199,16 L199,144 Q199,159 184,159 L16,159 Q1,159 1,144 L1,16 Q1,1 16,1 Z"
        android:strokeColor="#E0E0E0"
        android:strokeWidth="1" />

    <!-- Sun icon -->
    <path
        android:fillColor="#FFA726"
        android:pathData="M100,40 m-15,0 a15,15 0,1 1,30 0 a15,15 0,1 1,-30 0" />

    <!-- Temperature text placeholder -->
    <path
        android:fillColor="#333333"
        android:pathData="M85,75 L115,75 L115,85 L85,85 Z" />

    <!-- City name placeholder -->
    <path
        android:fillColor="#666666"
        android:pathData="M75,95 L125,95 L125,105 L75,105 Z" />

    <!-- Additional info placeholder -->
    <path
        android:fillColor="#999999"
        android:pathData="M70,120 L130,120 L130,125 L70,125 Z" />

</vector>
