package io.despicable.chromeos.presentation.navigation

import kotlinx.serialization.Serializable

/**
 * Type-safe navigation routes for the weather app
 */
sealed interface WeatherRoutes {
    
    /**
     * Home screen route showing list of weather locations
     */
    @Serializable
    object Home : WeatherRoutes
    
    /**
     * Weather detail screen route for a specific location
     * @param weatherId The ID of the weather location to display
     */
    @Serializable
    data class WeatherDetail(val weatherId: Long) : WeatherRoutes
}
