package io.despicable.chromeos.util

import io.despicable.chromeos.domain.model.WeatherCondition
import io.despicable.chromeos.domain.model.WeatherData
import kotlin.random.Random

/**
 * Utility class for generating sample weather data for testing
 */
object SampleWeatherData {

    private val cities = listOf(
        "New York", "London", "Tokyo", "Paris", "Sydney",
        "Berlin", "Toronto", "Mumbai", "Singapore", "Dubai",
        "Los Angeles", "Chicago", "Barcelona", "Amsterdam", "Seoul"
    )

    /**
     * Generate sample weather data for testing
     */
    fun getSampleWeatherData(): List<WeatherData> {
        return cities.mapIndexed { index, city ->
            val temperature = Random.nextInt(-10, 40)
            WeatherData(
                id = index.toLong() + 1,
                cityName = city,
                temperature = temperature, // -10°C to 40°C
                weatherCondition = WeatherCondition.values().random(),
                humidity = Random.nextInt(20, 90), // 20% to 90%
                windSpeed = Random.nextDouble(0.0, 30.0), // 0 to 30 km/h
                pressure = Random.nextDouble(980.0, 1040.0), // 980 to 1040 hPa
                visibility = Random.nextDouble(1.0, 15.0), // 1 to 15 km
                uvIndex = Random.nextInt(0, 12), // UV index 0-11
                feelsLike = temperature + Random.nextInt(-5, 6), // feels like temperature
                lastUpdated = System.currentTimeMillis() - Random.nextLong(0, 3600000) // Within last hour
            )
        }
    }

    /**
     * Get weather data for a specific city (for testing)
     */
    fun getWeatherForCity(cityName: String): WeatherData? {
        return getSampleWeatherData().find { it.cityName == cityName }
    }

    /**
     * Get list of available cities
     */
    fun getAvailableCities(): List<String> {
        return cities
    }

    /**
     * Generate random weather data for a city
     */
    fun generateRandomWeatherData(cityName: String): WeatherData {
        val temperature = Random.nextInt(-10, 40)
        return WeatherData(
            cityName = cityName,
            temperature = temperature,
            weatherCondition = WeatherCondition.values().random(),
            humidity = Random.nextInt(20, 90),
            windSpeed = Random.nextDouble(0.0, 30.0),
            pressure = Random.nextDouble(980.0, 1040.0),
            visibility = Random.nextDouble(1.0, 15.0),
            uvIndex = Random.nextInt(0, 12),
            feelsLike = temperature + Random.nextInt(-5, 6),
            lastUpdated = System.currentTimeMillis()
        )
    }
}
