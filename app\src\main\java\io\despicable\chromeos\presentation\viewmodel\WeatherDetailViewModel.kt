package io.despicable.chromeos.presentation.viewmodel

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import io.despicable.chromeos.domain.model.WeatherData
import io.despicable.chromeos.domain.usecase.GetWeatherDataUseCase
import io.despicable.chromeos.presentation.navigation.WeatherRoutes
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.launch

/**
 * ViewModel for the Weather Detail screen
 */
class WeatherDetailViewModel(
    private val savedStateHandle: SavedStateHandle,
    private val getWeatherDataUseCase: GetWeatherDataUseCase
) : ViewModel() {

    private val weatherId: Long = savedStateHandle.get<Long>("weatherId") ?: 0L

    private val _uiState = MutableStateFlow(WeatherDetailUiState())
    val uiState: StateFlow<WeatherDetailUiState> = _uiState.asStateFlow()

    init {
        loadWeatherDetail()
    }

    /**
     * Load weather detail for the specific location
     */
    private fun loadWeatherDetail() {
        viewModelScope.launch {
            getWeatherDataUseCase.getAllWeatherData()
                .onStart {
                    _uiState.value = _uiState.value.copy(isLoading = true, error = null)
                }
                .catch { exception ->
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = exception.message ?: "Failed to load weather details"
                    )
                }
                .collect { weatherList ->
                    val weatherData = weatherList.find { it.id == weatherId }
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        weatherData = weatherData,
                        error = if (weatherData == null) "Weather data not found" else null
                    )
                }
        }
    }

    /**
     * Refresh weather detail
     */
    fun refreshWeatherDetail() {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isRefreshing = true)
                getWeatherDataUseCase.refreshWeatherData()
                // Data will be automatically updated through the flow
            } catch (exception: Exception) {
                _uiState.value = _uiState.value.copy(
                    isRefreshing = false,
                    error = exception.message ?: "Failed to refresh weather data"
                )
            } finally {
                _uiState.value = _uiState.value.copy(isRefreshing = false)
            }
        }
    }

    /**
     * Clear error state
     */
    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }
}

/**
 * UI state for the Weather Detail screen
 */
data class WeatherDetailUiState(
    val isLoading: Boolean = false,
    val isRefreshing: Boolean = false,
    val weatherData: WeatherData? = null,
    val error: String? = null
)
