{"logs": [{"outputFile": "io.despicable.chromeos.app-mergeDebugResources-80:/values-hy/values-hy.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c91fc286a144f7dd3fed97bfa6a44d86\\transformed\\material3-release\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,286,401,517,616,718,837,983,1106,1262,1349,1447,1542,1641,1763,1885,1988,2128,2266,2399,2576,2705,2821,2940,3063,3159,3257,3380,3521,3627,3732,3840,3979,4123,4232,4334,4425,4520,4616,4723,4811,4896,5010,5090,5173,5272,5373,5464,5560,5649,5753,5851,5951,6068,6148,6253", "endColumns": "115,114,114,115,98,101,118,145,122,155,86,97,94,98,121,121,102,139,137,132,176,128,115,118,122,95,97,122,140,105,104,107,138,143,108,101,90,94,95,106,87,84,113,79,82,98,100,90,95,88,103,97,99,116,79,104,93", "endOffsets": "166,281,396,512,611,713,832,978,1101,1257,1344,1442,1537,1636,1758,1880,1983,2123,2261,2394,2571,2700,2816,2935,3058,3154,3252,3375,3516,3622,3727,3835,3974,4118,4227,4329,4420,4515,4611,4718,4806,4891,5005,5085,5168,5267,5368,5459,5555,5644,5748,5846,5946,6063,6143,6248,6342"}, "to": {"startLines": "47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4713,4829,4944,5059,5175,5274,5376,5495,5641,5764,5920,6007,6105,6200,6299,6421,6543,6646,6786,6924,7057,7234,7363,7479,7598,7721,7817,7915,8038,8179,8285,8390,8498,8637,8781,8890,8992,9083,9178,9274,9381,9469,9554,9668,9748,9831,9930,10031,10122,10218,10307,10411,10509,10609,10726,10806,10911", "endColumns": "115,114,114,115,98,101,118,145,122,155,86,97,94,98,121,121,102,139,137,132,176,128,115,118,122,95,97,122,140,105,104,107,138,143,108,101,90,94,95,106,87,84,113,79,82,98,100,90,95,88,103,97,99,116,79,104,93", "endOffsets": "4824,4939,5054,5170,5269,5371,5490,5636,5759,5915,6002,6100,6195,6294,6416,6538,6641,6781,6919,7052,7229,7358,7474,7593,7716,7812,7910,8033,8174,8280,8385,8493,8632,8776,8885,8987,9078,9173,9269,9376,9464,9549,9663,9743,9826,9925,10026,10117,10213,10302,10406,10504,10604,10721,10801,10906,11000"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ee982a82fb86e03ba63249f20db390ab\\transformed\\core-1.16.0\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,260,358,457,562,664,775", "endColumns": "99,104,97,98,104,101,110,100", "endOffsets": "150,255,353,452,557,659,770,871"}, "to": {"startLines": "30,31,32,33,34,35,36,115", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2893,2993,3098,3196,3295,3400,3502,11925", "endColumns": "99,104,97,98,104,101,110,100", "endOffsets": "2988,3093,3191,3290,3395,3497,3608,12021"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d531cb9ac8d38fdf826cdbc25dccf69e\\transformed\\ui-release\\res\\values-hy\\values-hy.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "185,284,366,460,560,643,725,811,906,988,1073,1161,1235,1323,1400,1479,1556,1637,1706", "endColumns": "98,81,93,99,82,81,85,94,81,84,87,73,87,76,78,76,80,68,117", "endOffsets": "279,361,455,555,638,720,806,901,983,1068,1156,1230,1318,1395,1474,1551,1632,1701,1819"}, "to": {"startLines": "37,38,39,40,41,45,46,105,106,107,108,110,111,112,113,114,116,117,118", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3613,3712,3794,3888,3988,4545,4627,11097,11192,11274,11359,11530,11604,11692,11769,11848,12026,12107,12176", "endColumns": "98,81,93,99,82,81,85,94,81,84,87,73,87,76,78,76,80,68,117", "endOffsets": "3707,3789,3883,3983,4066,4622,4708,11187,11269,11354,11442,11599,11687,11764,11843,11920,12102,12171,12289"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\73e1d3faf204d8dc115e2ef9096d86cb\\transformed\\foundation-release\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,134,219", "endColumns": "78,84,88", "endOffsets": "129,214,303"}, "to": {"startLines": "29,119,120", "startColumns": "4,4,4", "startOffsets": "2814,12294,12379", "endColumns": "78,84,88", "endOffsets": "2888,12374,12463"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2a5a5ac5787a9ad7f21a44018aa6dab8\\transformed\\appcompat-1.7.0\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,313,423,512,618,735,817,897,988,1081,1176,1270,1370,1463,1558,1652,1743,1834,1917,2023,2129,2228,2338,2446,2547,2717,2814", "endColumns": "107,99,109,88,105,116,81,79,90,92,94,93,99,92,94,93,90,90,82,105,105,98,109,107,100,169,96,82", "endOffsets": "208,308,418,507,613,730,812,892,983,1076,1171,1265,1365,1458,1553,1647,1738,1829,1912,2018,2124,2223,2333,2441,2542,2712,2809,2892"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,109", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,313,423,512,618,735,817,897,988,1081,1176,1270,1370,1463,1558,1652,1743,1834,1917,2023,2129,2228,2338,2446,2547,2717,11447", "endColumns": "107,99,109,88,105,116,81,79,90,92,94,93,99,92,94,93,90,90,82,105,105,98,109,107,100,169,96,82", "endOffsets": "208,308,418,507,613,730,812,892,983,1076,1171,1265,1365,1458,1553,1647,1738,1829,1912,2018,2124,2223,2333,2441,2542,2712,2809,11525"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\834bf615f3749af516fa6fc397086833\\transformed\\material-release\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "91", "endOffsets": "142"}, "to": {"startLines": "104", "startColumns": "4", "startOffsets": "11005", "endColumns": "91", "endOffsets": "11092"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\0f1e3221b145c9b74bd642a03e5d8a29\\transformed\\glance-appwidget-1.1.1\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,277,402", "endColumns": "221,124,126", "endOffsets": "272,397,524"}, "to": {"startLines": "42,43,44", "startColumns": "4,4,4", "startOffsets": "4071,4293,4418", "endColumns": "221,124,126", "endOffsets": "4288,4413,4540"}}]}]}