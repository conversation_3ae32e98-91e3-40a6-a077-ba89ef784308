package io.despicable.chromeos.di

import io.despicable.chromeos.presentation.viewmodel.HomeViewModel
import io.despicable.chromeos.presentation.viewmodel.WeatherDetailViewModel
import io.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel
import org.koin.androidx.viewmodel.dsl.viewModel
import org.koin.dsl.module

/**
 * Koin module for ViewModel dependencies
 */
val viewModelModule = module {

    // Provide HomeViewModel
    viewModel {
        HomeViewModel(
            getWeatherDataUseCase = get()
        )
    }

    // Provide WeatherDetailViewModel
    viewModel {
        WeatherDetailViewModel(
            savedStateHandle = get(),
            getWeatherDataUseCase = get()
        )
    }

    // Provide WidgetConfigViewModel factory
    factory { (widgetId: Int) ->
        WidgetConfigViewModel(
            widgetId = widgetId,
            getWeatherDataUseCase = get(),
            updateWidgetConfigUseCase = get()
        )
    }
}
