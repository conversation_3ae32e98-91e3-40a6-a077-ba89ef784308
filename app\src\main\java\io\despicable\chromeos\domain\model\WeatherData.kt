package io.despicable.chromeos.domain.model

import kotlinx.serialization.Serializable

/**
 * Domain model representing weather data
 */
@Serializable
data class WeatherData(
    val id: Long = 0,
    val cityName: String,
    val temperature: Int, // in Celsius
    val weatherCondition: WeatherCondition,
    val humidity: Int, // percentage
    val windSpeed: Double, // km/h
    val pressure: Double = 1013.25, // hPa
    val visibility: Double = 10.0, // km
    val uvIndex: Int = 0, // UV index 0-11
    val feelsLike: Int = temperature, // feels like temperature
    val lastUpdated: Long = System.currentTimeMillis()
)

/**
 * Weather condition types with corresponding icons
 */
@Serializable
enum class WeatherCondition(val displayName: String, val iconName: String) {
    SUNNY("Sunny", "sunny"),
    CLOUDY("Cloudy", "cloudy"),
    RAINY("Rainy", "rainy"),
    SNOWY("Snowy", "snowy"),
    STORMY("Stormy", "stormy"),
    FOGGY("Foggy", "foggy"),
    PARTLY_CLOUDY("Partly Cloudy", "partly_cloudy")
}
