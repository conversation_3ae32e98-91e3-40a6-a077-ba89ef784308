package io.despicable.chromeos.domain.model

import kotlinx.serialization.Serializable

/**
 * Domain model representing widget configuration settings
 */
@Serializable
data class WidgetConfiguration(
    val widgetId: Int,
    val cityName: String,
    val iconSize: IconSize = IconSize.MEDIUM,
    val fontSize: FontSize = FontSize.MEDIUM,
    val showHumidity: Boolean = true,
    val showWindSpeed: Boolean = true,
    val backgroundColor: BackgroundColor = BackgroundColor.SYSTEM,
    val transparency: Float = 0.9f, // 0.0 = fully transparent, 1.0 = fully opaque
    val lastUpdated: Long = System.currentTimeMillis()
)

/**
 * Available icon sizes for the weather widget
 */
@Serializable
enum class IconSize(val displayName: String, val sizeDp: Int) {
    SMALL("Small", 32),
    MEDIUM("Medium", 48),
    LARGE("Large", 64),
    EXTRA_LARGE("Extra Large", 80)
}

/**
 * Available font sizes for the widget text
 */
@Serializable
enum class FontSize(val displayName: String, val sizeSp: Int) {
    SMALL("Small", 12),
    MEDIUM("Medium", 16),
    LARGE("Large", 20),
    EXTRA_LARGE("Extra Large", 24)
}

/**
 * Available background colors for the weather widget
 */
@Serializable
enum class BackgroundColor(val displayName: String, val colorValue: Long) {
    BLACK("Black", 0xFF000000),
    WHITE("White", 0xFFFFFFFF),
    BLUE("Blue", 0xFF2196F3),
    SYSTEM("System/Dynamic", 0x00000000) // Special value for dynamic color
}
