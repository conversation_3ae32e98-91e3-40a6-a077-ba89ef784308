{"logs": [{"outputFile": "io.despicable.chromeos.app-mergeDebugResources-80:/values-ca/values-ca.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\834bf615f3749af516fa6fc397086833\\transformed\\material-release\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "88", "endOffsets": "139"}, "to": {"startLines": "104", "startColumns": "4", "startOffsets": "11135", "endColumns": "88", "endOffsets": "11219"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\0f1e3221b145c9b74bd642a03e5d8a29\\transformed\\glance-appwidget-1.1.1\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,250,367", "endColumns": "194,116,117", "endOffsets": "245,362,480"}, "to": {"startLines": "42,43,44", "startColumns": "4,4,4", "startOffsets": "4127,4322,4439", "endColumns": "194,116,117", "endOffsets": "4317,4434,4552"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\73e1d3faf204d8dc115e2ef9096d86cb\\transformed\\foundation-release\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,144,242", "endColumns": "88,97,101", "endOffsets": "139,237,339"}, "to": {"startLines": "29,119,120", "startColumns": "4,4,4", "startOffsets": "2830,12424,12522", "endColumns": "88,97,101", "endOffsets": "2914,12517,12619"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ee982a82fb86e03ba63249f20db390ab\\transformed\\core-1.16.0\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,449,555,660,786", "endColumns": "95,101,98,96,105,104,125,100", "endOffsets": "146,248,347,444,550,655,781,882"}, "to": {"startLines": "30,31,32,33,34,35,36,115", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2919,3015,3117,3216,3313,3419,3524,12050", "endColumns": "95,101,98,96,105,104,125,100", "endOffsets": "3010,3112,3211,3308,3414,3519,3645,12146"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d531cb9ac8d38fdf826cdbc25dccf69e\\transformed\\ui-release\\res\\values-ca\\values-ca.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "195,292,376,480,583,672,750,841,932,1018,1104,1195,1271,1356,1431,1510,1585,1667,1738", "endColumns": "96,83,103,102,88,77,90,90,85,85,90,75,84,74,78,74,81,70,119", "endOffsets": "287,371,475,578,667,745,836,927,1013,1099,1190,1266,1351,1426,1505,1580,1662,1733,1853"}, "to": {"startLines": "37,38,39,40,41,45,46,105,106,107,108,110,111,112,113,114,116,117,118", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3650,3747,3831,3935,4038,4557,4635,11224,11315,11401,11487,11660,11736,11821,11896,11975,12151,12233,12304", "endColumns": "96,83,103,102,88,77,90,90,85,85,90,75,84,74,78,74,81,70,119", "endOffsets": "3742,3826,3930,4033,4122,4630,4721,11310,11396,11482,11573,11731,11816,11891,11970,12045,12228,12299,12419"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c91fc286a144f7dd3fed97bfa6a44d86\\transformed\\material3-release\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,176,295,414,534,634,732,847,989,1104,1263,1347,1445,1543,1644,1761,1890,1993,2134,2274,2415,2581,2714,2831,2952,3081,3180,3277,3398,3543,3649,3762,3876,4015,4160,4269,4376,4462,4563,4664,4775,4861,4947,5058,5138,5222,5323,5431,5530,5634,5721,5834,5934,6041,6160,6240,6357", "endColumns": "120,118,118,119,99,97,114,141,114,158,83,97,97,100,116,128,102,140,139,140,165,132,116,120,128,98,96,120,144,105,112,113,138,144,108,106,85,100,100,110,85,85,110,79,83,100,107,98,103,86,112,99,106,118,79,116,106", "endOffsets": "171,290,409,529,629,727,842,984,1099,1258,1342,1440,1538,1639,1756,1885,1988,2129,2269,2410,2576,2709,2826,2947,3076,3175,3272,3393,3538,3644,3757,3871,4010,4155,4264,4371,4457,4558,4659,4770,4856,4942,5053,5133,5217,5318,5426,5525,5629,5716,5829,5929,6036,6155,6235,6352,6459"}, "to": {"startLines": "47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4726,4847,4966,5085,5205,5305,5403,5518,5660,5775,5934,6018,6116,6214,6315,6432,6561,6664,6805,6945,7086,7252,7385,7502,7623,7752,7851,7948,8069,8214,8320,8433,8547,8686,8831,8940,9047,9133,9234,9335,9446,9532,9618,9729,9809,9893,9994,10102,10201,10305,10392,10505,10605,10712,10831,10911,11028", "endColumns": "120,118,118,119,99,97,114,141,114,158,83,97,97,100,116,128,102,140,139,140,165,132,116,120,128,98,96,120,144,105,112,113,138,144,108,106,85,100,100,110,85,85,110,79,83,100,107,98,103,86,112,99,106,118,79,116,106", "endOffsets": "4842,4961,5080,5200,5300,5398,5513,5655,5770,5929,6013,6111,6209,6310,6427,6556,6659,6800,6940,7081,7247,7380,7497,7618,7747,7846,7943,8064,8209,8315,8428,8542,8681,8826,8935,9042,9128,9229,9330,9441,9527,9613,9724,9804,9888,9989,10097,10196,10300,10387,10500,10600,10707,10826,10906,11023,11130"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2a5a5ac5787a9ad7f21a44018aa6dab8\\transformed\\appcompat-1.7.0\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,228,333,440,523,629,755,839,918,1009,1102,1195,1290,1388,1481,1574,1668,1759,1850,1931,2042,2150,2248,2358,2463,2571,2731,2830", "endColumns": "122,104,106,82,105,125,83,78,90,92,92,94,97,92,92,93,90,90,80,110,107,97,109,104,107,159,98,81", "endOffsets": "223,328,435,518,624,750,834,913,1004,1097,1190,1285,1383,1476,1569,1663,1754,1845,1926,2037,2145,2243,2353,2458,2566,2726,2825,2907"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,109", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,228,333,440,523,629,755,839,918,1009,1102,1195,1290,1388,1481,1574,1668,1759,1850,1931,2042,2150,2248,2358,2463,2571,2731,11578", "endColumns": "122,104,106,82,105,125,83,78,90,92,92,94,97,92,92,93,90,90,80,110,107,97,109,104,107,159,98,81", "endOffsets": "223,328,435,518,624,750,834,913,1004,1097,1190,1285,1383,1476,1569,1663,1754,1845,1926,2037,2145,2243,2353,2458,2566,2726,2825,11655"}}]}]}